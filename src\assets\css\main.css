@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-neutral-50 text-neutral-800 font-sans;
  }

  /* 标题样式统一 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-serif font-bold text-neutral-900 tracking-wide;
  }

  /* 链接样式统一 */
  a {
    @apply transition-colors duration-300;
  }
}

@layer components {
  /* === 按钮组件系统 === */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  /* 主要按钮 - 深蓝色 */
  .btn-primary {
    @apply bg-primary-800 text-white hover:bg-primary-700 focus:ring-primary-500;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(30, 64, 175, 0.35);
  }

  /* 辅助按钮 - 橙色 */
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
    box-shadow: 0 4px 12px rgba(234, 88, 12, 0.25);
  }

  .btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(234, 88, 12, 0.35);
  }

  /* 轮廓按钮 */
  .btn-outline {
    @apply border-2 border-primary-800 text-primary-800 hover:bg-primary-800 hover:text-white focus:ring-primary-500;
  }

  /* 成功按钮 */
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  /* 警告按钮 */
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  /* 错误按钮 */
  .btn-error {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  /* 小尺寸按钮 */
  .btn-sm {
    @apply px-4 py-2 text-sm;
  }

  /* 大尺寸按钮 */
  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  /* === 卡片组件系统 === */
  .card {
    @apply bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 border border-neutral-200;
  }

  .card:hover {
    @apply shadow-xl border-primary-200;
    transform: translateY(-4px);
  }

  /* 资源卡片 */
  .resource-card {
    @apply bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 border-l-4 border-l-primary-800;
  }

  .resource-card:hover {
    @apply shadow-xl border-l-secondary-600;
    transform: translateY(-4px);
  }

  /* === 布局组件 === */
  .section {
    @apply py-16 md:py-24;
  }

  .container {
    @apply px-4 mx-auto max-w-7xl;
  }

  /* === 导航组件 === */
  .nav-link {
    @apply px-4 py-2 text-neutral-600 hover:text-primary-800 transition-colors duration-300 rounded-md;
  }

  .nav-link:hover {
    @apply bg-primary-50;
  }

  .nav-link-active {
    @apply text-primary-800 font-medium bg-primary-100;
  }

  /* === 社交图标 === */
  .social-icon {
    @apply w-10 h-10 flex items-center justify-center rounded-lg text-neutral-600 hover:text-primary-800 hover:bg-primary-50 transition-all duration-300;
  }

  /* === 标签组件 === */
  .tech-tag {
    @apply inline-block px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 transition-all duration-300;
  }

  .tech-tag:hover {
    @apply bg-primary-200 text-primary-900;
  }

  /* 状态标签 */
  .tag-success {
    @apply bg-success-100 text-success-800;
  }

  .tag-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .tag-error {
    @apply bg-error-100 text-error-800;
  }

  .tag-info {
    @apply bg-info-100 text-info-800;
  }
}

/* === 自定义滚动条 === */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-400 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-neutral-500;
}

/* === 工具类 === */
.gradient-primary {
  background: linear-gradient(135deg, theme('colors.primary.800'), theme('colors.primary.600'));
}

.gradient-secondary {
  background: linear-gradient(135deg, theme('colors.secondary.600'), theme('colors.secondary.500'));
}

.gradient-accent {
  background: linear-gradient(135deg, theme('colors.primary.800'), theme('colors.secondary.600'));
}

/* === 动画增强 === */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}