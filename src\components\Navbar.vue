<template>
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <nav class="container mx-auto px-4 py-3 flex items-center justify-between">
      <!-- Logo -->
      <router-link to="/" class="flex items-center space-x-2">
        <div class="w-8 h-8 rounded-full overflow-hidden">
          <img :src="siteConfig.site.logo" :alt="siteConfig.site.title" class="w-full h-full object-cover" />
        </div>
        <span class="text-xl font-bold text-primary-800">{{ siteConfig.site.title }}</span>
      </router-link>
      
      <!-- 移动端菜单按钮 -->
      <button @click="isMenuOpen = !isMenuOpen" 
        class="md:hidden p-2 rounded-full text-lg focus:outline-none text-gray-500 hover:bg-gray-100">
        <i :class="isMenuOpen ? 'fa-solid fa-xmark' : 'fa-solid fa-bars'"></i>
      </button>
      
      <!-- 桌面端导航链接 -->
      <div class="hidden md:flex items-center space-x-1">
        <router-link v-for="item in siteConfig.navItems" :key="item.path" :to="item.path"
          class="nav-link"
          :class="[
            $route.path === item.path ? 'nav-link-active' : ''
          ]">
          {{ item.name }}
        </router-link>
      </div>
    </nav>
    
    <!-- 移动端导航菜单 -->
    <div v-if="isMenuOpen" class="md:hidden bg-white border-t border-gray-100">
      <div class="container mx-auto px-4 py-2">
        <router-link v-for="item in siteConfig.navItems" :key="item.path" :to="item.path"
          class="block py-3 border-b border-neutral-100 transition-colors duration-300 px-4 rounded-md"
          :class="[
            $route.path === item.path ? 'text-primary-800 font-medium bg-primary-50' : 'text-neutral-700 hover:text-primary-800 hover:bg-primary-50'
          ]"
          @click="isMenuOpen = false">
          {{ item.name }}
        </router-link>
      </div>
    </div>
  </header>
</template>

<script>
import { ref } from 'vue';
import siteConfig from '../config/site.js';

export default {
  name: 'Navbar',
  setup() {
    const isMenuOpen = ref(false);

    return {
      isMenuOpen,
      siteConfig
    };
  },
  watch: {
    '$route'() {
      this.isMenuOpen = false;
    }
  }
}
</script>

<style>
.nav-link {
  @apply px-4 py-2 rounded-md;
}

/* 导航样式已在main.css中统一定义 */
</style> 