/**
 * 网站配置文件
 * 在这里集中管理网站的所有配置信息，修改此文件即可自定义网站内容
 */

// 导入图片资源
import logoImage from '../assets/images/logo.jpeg';
import faviconImage from '../assets/images/favicon.ico';
import kawaiiPoster from '../assets/images/kawaii/poster.webp';
import kawaiiTsukasa from '../assets/images/kawaii/tsukasa.webp';
import kawaiiNasa from '../assets/images/kawaii/nasa.webp';
import kawaiiKaname from '../assets/images/kawaii/kaname.webp';
import kawaiiScene1 from '../assets/images/kawaii/scene1.avif';
import kawaiiScene2 from '../assets/images/kawaii/scene2.avif';
import kawaiiScene3 from '../assets/images/kawaii/scene3.avif';

const siteConfig = {
  // 基本信息
  site: {
    title: "轩轩的资源分享", // 网站标题
    description: "技术资源 / 软件分享 / 实用工具", // 网站描述
    author: "轩轩", // 作者名称
    email: "<EMAIL>", // 联系邮箱
    qq: "123456789", // QQ号码
    githubUsername: "xuanxuan", // GitHub用户名
    logo: logoImage, // Logo路径
    favicon: faviconImage, // 网站图标
    language: "zh-CN", // 网站默认语言
    // ICP备案信息
    icp: {
      number: "鲁ICP备2025181732号-1", // 请替换为您的实际ICP备案号
      link: "https://beian.mps.gov.cn/#/query/webSearchx", // 工信部备案管理系统链接
      showIcon: true // 是否显示备案图标
    },
  },

  // 社交媒体链接
  socialLinks: [
    { name: "博客", url: "https://blog.xuanxuan.com", icon: "fas fa-blog" },
    { name: "哔哩哔哩", url: "https://space.bilibili.com/xuanxuan", icon: "fab fa-bilibili" },
    { name: "GitHub", url: "https://github.com/xuanxuan", icon: "fab fa-github" },
    { name: "QQ群", url: "https://qm.qq.com/cgi-bin/qm/qr?k=example", icon: "fab fa-qq" },
    { name: "微信群", url: "#", icon: "fab fa-weixin" },
    { name: "邮箱", url: "mailto:<EMAIL>", icon: "fas fa-envelope" }
  ],

  // 导航菜单
  navItems: [
    { name: "首页", path: "/" },
    { name: "资源库", path: "/projects" },
    { name: "技术文章", path: "/articles" },
    { name: "关于", path: "/about" }
  ],

  // 首页精选资源
  featuredProjects: [
    {
      name: "开发工具集合",
      description: "精选的开发工具、IDE插件、代码片段等实用资源，提升开发效率",
      github: "https://github.com/xuanxuan/dev-tools"
    },
    {
      name: "系统优化工具",
      description: "Windows/Mac/Linux系统优化脚本和工具集合，让你的系统运行更流畅",
      github: "https://github.com/xuanxuan/system-optimizer"
    },
    {
      name: "学习资源库",
      description: "编程学习资料、电子书、视频教程等优质学习资源整理",
      github: "https://github.com/xuanxuan/learning-resources"
    }
  ],

  // --- 作品集页面配置 ---
  projectsPage: {
    // 在这里切换模式:
    // 'hybrid': GitHub Pinned 项目 + 下方的自定义项目列表
    // 'custom': 只显示下方自定义项目列表中的项目
    mode: 'hybrid', // 您可以在 'hybrid' 和 'custom' 之间切换

    // 自定义项目列表
    // 在 'hybrid' 模式下，它们会补充在 GitHub 项目之后
    // 在 'custom' 模式下，它们是唯一显示的项目
    customProjects: [
      { repo: "sindresorhus/awesome" },
      { repo: "vinta/awesome-python" },
      // 您可以在这里添加任何想补充的公开仓库
    ]
  },


  // 首页展示的最新文章
  latestArticles: [
    {
      id: "dev-tools-2024", // 建议使用文件名作为id，以保证能正确跳转
      title: "2024年最佳开发工具推荐",
      summary: "整理了2024年最值得使用的开发工具，包括编辑器、调试工具、性能分析工具等，助力提升开发效率。"
    },
    {
      id: "system-optimization-guide", // 建议使用文件名作为id
      title: "系统性能优化完全指南",
      summary: "从硬件到软件，从系统设置到应用优化，全方位提升你的计算机性能，让工作更高效。"
    }
  ],


  // 主题配置
  theme: {
    enableDarkMode: true, // 是否启用暗色模式
    defaultTheme: "light", // 默认主题模式：light或dark
  },

  // 国际化配置
  i18n: {
    enabled: false, // 是否启用多语言
    supportedLocales: ["zh-CN", "en-US"], // 支持的语言列表
    defaultLocale: "zh-CN", // 默认语言
  },

  // 《总之就是非常可爱》页面配置
  kawaii: {
    // 海报图片
    poster: kawaiiPoster, // 海报图片路径，需要将图片放在此路径
    // 动画信息
    animeInfo: {
      title: "总之就是非常可爱",
      titleEn: "Fly Me to the Moon",
      releaseDate: "2020年10月",
      studio: "Seven Arcs",
      episodes: "8集 + OVA",
      description: "由崎星空对神秘美少女——司一见钟情。面对星空决死的告白，她的回答是\"如果你愿意和我结婚，那我就跟你交往\"？！充满了星空与司的爱，可爱&高贵的新婚生活开始了！"
    },
    // 主要角色
    characters: [
      {
        name: "由崎 司",
        image: kawaiiTsukasa,
        description: "女主角，美丽神秘的少女，星空一见钟情的女子。有着超越年龄的成熟和智慧，总之就是超级无敌非常可爱！"
      },
      {
        name: "由崎 星空",
        image: kawaiiNasa, // 角色图片路径
        description: "男主角，天才少年，因与司结婚而获得新生。头脑聪明，但在感性方面与常人迥异。"
      },
      {
        name: "有栖川 要",
        image: kawaiiKaname,
        description: "星空国中时的学妹，星空家附近的钱汤\"草津温泉风汤布院\"老板的女儿。"
      }
    ],
    // 精彩场景
    scenes: [
      kawaiiScene1,
      kawaiiScene2,
      kawaiiScene3
    ]
  }
};

export default siteConfig; 