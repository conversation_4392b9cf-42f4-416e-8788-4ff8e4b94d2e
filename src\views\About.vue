<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-neutral-50">
    <!-- Hero Section -->
    <section class="pt-24 pb-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16" data-aos="fade-up">
          <h1 class="text-4xl md:text-5xl font-bold mb-6 text-neutral-900">
            关于<span class="text-primary-800">轩轩的资源分享</span>
          </h1>
          <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
            致力于为开发者和技术爱好者提供优质、实用的技术资源和工具分享
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-12 items-center">
          <div data-aos="fade-right">
            <div class="gradient-accent rounded-xl p-8 text-white">
              <div class="text-center">
                <i class="fas fa-code text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold mb-4">技术驱动</h3>
                <p class="text-lg opacity-90">
                  专注于前沿技术的研究与应用，为用户提供最新、最实用的技术资源
                </p>
              </div>
            </div>
          </div>

          <div data-aos="fade-left">
            <h2 class="text-3xl font-bold mb-6 text-neutral-900">我的使命</h2>
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-check-circle text-success-600 mt-1 mr-3"></i>
                <p class="text-neutral-700">精选优质的开发工具和软件资源</p>
              </div>
              <div class="flex items-start">
                <i class="fas fa-check-circle text-success-600 mt-1 mr-3"></i>
                <p class="text-neutral-700">分享实用的技术教程和学习资料</p>
              </div>
              <div class="flex items-start">
                <i class="fas fa-check-circle text-success-600 mt-1 mr-3"></i>
                <p class="text-neutral-700">构建活跃的技术交流社区</p>
              </div>
              <div class="flex items-start">
                <i class="fas fa-check-circle text-success-600 mt-1 mr-3"></i>
                <p class="text-neutral-700">帮助更多人提升工作效率</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-neutral-900">资源特色</h2>
          <div class="w-20 h-1 bg-primary-800 mx-auto"></div>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="resource-card p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="text-center">
              <i class="fas fa-tools text-4xl text-primary-800 mb-4"></i>
              <h3 class="text-xl font-bold mb-3 text-neutral-800">开发工具</h3>
              <p class="text-neutral-600">
                精选的IDE、编辑器、调试工具等开发必备软件，提升编程效率
              </p>
            </div>
          </div>

          <div class="resource-card p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="text-center">
              <i class="fas fa-cogs text-4xl text-secondary-600 mb-4"></i>
              <h3 class="text-xl font-bold mb-3 text-neutral-800">系统优化</h3>
              <p class="text-neutral-600">
                系统清理、性能优化、安全防护等实用工具，让系统运行更流畅
              </p>
            </div>
          </div>

          <div class="resource-card p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="text-center">
              <i class="fas fa-book text-4xl text-info-600 mb-4"></i>
              <h3 class="text-xl font-bold mb-3 text-neutral-800">学习资源</h3>
              <p class="text-neutral-600">
                技术文档、视频教程、电子书等优质学习材料，助力技能提升
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-neutral-50">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-4 gap-8 text-center">
          <div data-aos="fade-up" data-aos-delay="100">
            <div class="text-4xl font-bold text-primary-800 mb-2">500+</div>
            <div class="text-neutral-600">精选资源</div>
          </div>
          <div data-aos="fade-up" data-aos-delay="200">
            <div class="text-4xl font-bold text-secondary-600 mb-2">50+</div>
            <div class="text-neutral-600">技术文章</div>
          </div>
          <div data-aos="fade-up" data-aos-delay="300">
            <div class="text-4xl font-bold text-primary-800 mb-2">1000+</div>
            <div class="text-neutral-600">用户下载</div>
          </div>
          <div data-aos="fade-up" data-aos-delay="400">
            <div class="text-4xl font-bold text-secondary-600 mb-2">24/7</div>
            <div class="text-neutral-600">在线支持</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-neutral-900">加入我们</h2>
          <div class="w-20 h-1 bg-primary-800 mx-auto mb-6"></div>
          <p class="text-xl text-neutral-600 max-w-2xl mx-auto">
            有好的资源推荐？想要交流技术？欢迎加入我们的社区！
          </p>
        </div>

        <div class="flex justify-center space-x-6">
          <a href="#" class="btn btn-primary">
            <i class="fab fa-qq mr-2"></i>
            加入QQ群
          </a>
          <a href="#" class="btn btn-secondary">
            <i class="fab fa-weixin mr-2"></i>
            微信交流
          </a>
          <a href="mailto:<EMAIL>" class="btn btn-outline">
            <i class="fas fa-envelope mr-2"></i>
            邮件联系
          </a>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { onMounted } from 'vue';
import AOS from 'aos';

export default {
  name: 'About',
  setup() {
    onMounted(() => {
      AOS.init({
        duration: 800,
        once: true
      });
    });
  }
}
</script>

<style scoped>
/* 组件特定样式 */
</style>
