<template>
  <div class="bg-neutral-50 min-h-screen">
    <div v-if="article" class="container mx-auto px-4 py-12">
      <div class="max-w-5xl mx-auto">
        <!-- 文章头部 -->
        <div class="bg-white rounded-t-xl p-8 shadow-sm w-full border-l-4 border-l-primary-800" data-aos="fade-up">
          <div class="flex items-center mb-4 text-sm text-neutral-500">
            <span class="flex items-center">
              <i class="far fa-calendar-alt mr-2"></i>
              {{ formatArticleDate(article.date) }}
            </span>
            <span class="mx-3">•</span>
            <span class="flex items-center">
              <i class="far fa-folder mr-2"></i>
              {{ article.category }}
            </span>
            <span v-if="article.author" class="mx-3">•</span>
            <span v-if="article.author" class="flex items-center">
              <i class="far fa-user mr-2"></i>
              {{ article.author }}
            </span>
          </div>

          <h1 class="text-3xl md:text-4xl font-bold mb-6 text-neutral-900">{{ article.title }}</h1>

          <div class="flex flex-wrap gap-2 mb-6">
            <span v-for="tag in article.tags" :key="tag"
              class="tech-tag text-xs">
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 文章内容 -->
        <div class="bg-white rounded-b-xl p-8 shadow-sm" data-aos="fade-up"
          data-aos-delay="100">
          <div class="prose prose-lg max-w-none" v-html="renderedContent"></div>
        </div>

        <!-- 导航按钮 -->
        <div class="mt-8 flex justify-between" data-aos="fade-up" data-aos-delay="200">
          <router-link to="/articles" class="btn btn-outline">
            <i class="fas fa-arrow-left mr-2"></i>
            返回文章列表
          </router-link>

          <div class="flex space-x-4">
            <button v-if="prevArticle" @click="navigateToArticle(prevArticle.id || prevArticle.slug)"
              class="btn btn-outline">
              <i class="fas fa-chevron-left mr-2"></i>
              上一篇
            </button>
            <button v-if="nextArticle" @click="navigateToArticle(nextArticle.id || nextArticle.slug)"
              class="btn btn-outline">
              下一篇
              <i class="fas fa-chevron-right ml-2"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-else class="container mx-auto px-4 py-24 text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-800"></div>
      <p class="mt-4 text-neutral-600">加载文章中...</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getArticleList, getArticleBySlug, formatDate } from '../utils/markdown';

export default {
  name: 'ArticleDetail',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const article = ref(null);
    const renderedContent = ref('');
    
    // 用于“上一篇/下一篇”导航
    const allArticlesList = ref([]); 
    const currentIndex = ref(-1);

    const loadArticleData = async () => {
      article.value = null; // 重置
      const slug = route.params.id;
      const foundArticle = await getArticleBySlug(slug);

      if (foundArticle) {
        article.value = foundArticle;
        renderedContent.value = foundArticle.renderedContent;
        
        // 更新当前文章在列表中的索引
        currentIndex.value = allArticlesList.value.findIndex(a => a.slug === slug);
      } else {
        router.push('/articles');
      }
    };

    const prevArticle = computed(() => {
      if (currentIndex.value > 0) {
        return allArticlesList.value[currentIndex.value - 1];
      }
      return null;
    });

    const nextArticle = computed(() => {
      if (currentIndex.value > -1 && currentIndex.value < allArticlesList.value.length - 1) {
        return allArticlesList.value[currentIndex.value + 1];
      }
      return null;
    });
    
    const navigateToArticle = (slug) => {
      router.push(`/article/${slug}`);
    };

    const formatArticleDate = (dateString) => {
      return formatDate(dateString);
    };

    // 监听路由参数变化，重新加载文章
    watch(() => route.params.id, loadArticleData);

    onMounted(async () => {
      // 先加载完整的文章列表用于导航，但不显示
      const { articleList: list } = await getArticleList();
      allArticlesList.value = list.value;
      // 然后加载当前需要的文章详情
      await loadArticleData();
    });

    return {
      article,
      renderedContent,
      prevArticle,
      nextArticle,
      navigateToArticle,
      formatArticleDate
    };
  }
}
</script>

<style>
/* 文章内容样式 - 使用新的色彩系统 */
.prose {
  max-width: none !important;
  color: theme('colors.neutral.700');
  width: 100%;
  line-height: 1.7;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: theme('colors.neutral.900');
  font-weight: 700;
}

.prose h1 {
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose h2 {
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
  border-bottom: 2px solid theme('colors.primary.200');
  padding-bottom: 0.5em;
}

.prose h3 {
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  color: theme('colors.primary.800');
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose a {
  color: theme('colors.primary.800');
  text-decoration: underline;
  text-decoration-color: theme('colors.primary.300');
  transition: all 0.3s ease;
}

.prose a:hover {
  color: theme('colors.primary.600');
  text-decoration-color: theme('colors.primary.600');
}

.prose pre {
  background-color: theme('colors.neutral.100');
  border: 1px solid theme('colors.neutral.200');
  border-radius: 0.5rem;
  padding: 1.5em;
  overflow-x: auto;
  margin: 1.5em 0;
}

.prose code {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.875em;
  color: theme('colors.primary.800');
  background-color: theme('colors.primary.50');
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  color: theme('colors.neutral.800');
}

.prose blockquote {
  border-left: 4px solid theme('colors.primary.800');
  background-color: theme('colors.primary.50');
  padding: 1em 1.5em;
  margin: 1.5em 0;
  font-style: italic;
  color: theme('colors.neutral.700');
}

.prose ul,
.prose ol {
  margin: 1.25em 0;
  padding-left: 1.5em;
}

.prose li {
  margin: 0.5em 0;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  border: 1px solid theme('colors.neutral.200');
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose table th {
  background-color: theme('colors.primary.100');
  color: theme('colors.primary.800');
  font-weight: 600;
  padding: 0.75em;
  text-align: left;
  border-bottom: 2px solid theme('colors.primary.200');
}

.prose table td {
  padding: 0.75em;
  border-bottom: 1px solid theme('colors.neutral.200');
}

.prose table tr:nth-child(even) {
  background-color: theme('colors.neutral.50');
}

.prose table tr:hover {
  background-color: theme('colors.primary.50');
}

.prose img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 1.5em 0;
}

.prose hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, theme('colors.primary.800'), theme('colors.secondary.600'));
  margin: 2em 0;
  border-radius: 1px;
}
</style>