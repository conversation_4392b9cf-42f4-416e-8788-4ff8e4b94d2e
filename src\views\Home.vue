<template>
  <div>
    <section class="bg-gradient-to-br from-primary-50 to-neutral-50 min-h-screen flex items-start pt-24 md:pt-40">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center">
          <div class="md:w-1/2 mb-10 md:mb-0" data-aos="fade-right">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-neutral-900">
              欢迎来到 <span class="text-primary-800">轩轩的自由分享</span>
            </h1>
            <p class="text-xl text-neutral-600 mb-8">
              专注于分享优质的技术资源、实用工具和软件，助力你的学习与工作效率提升。
            </p>
            <div class="flex flex-wrap gap-4">
              <router-link to="/projects" class="btn btn-primary">
                浏览资源库
                <i class="fas fa-download ml-2"></i>
              </router-link>
              <router-link to="/articles" class="btn btn-outline">
                技术文章
                <i class="fas fa-book ml-2"></i>
              </router-link>
              <router-link to="/about" class="btn btn-secondary">
                了解更多
                <i class="fas fa-info-circle ml-2"></i>
              </router-link>
            </div>
          </div>

          <div class="md:w-1/2 flex justify-center" data-aos="fade-left">
            <div class="relative">
              <div
                class="w-64 h-64 md:w-80 md:h-80 rounded-xl overflow-hidden border-4 border-white shadow-xl animate-float gradient-accent">
                <div class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <i class="fas fa-share-alt text-6xl mb-4"></i>
                    <h3 class="text-2xl font-bold">自由分享</h3>
                  </div>
                </div>
              </div>
              <div class="absolute -top-4 -right-4 w-20 h-20 bg-primary-600 rounded-full opacity-30 animate-pulse">
              </div>
              <div class="absolute -bottom-2 -left-6 w-16 h-16 bg-secondary-600 rounded-full opacity-30 animate-pulse">
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section bg-white">
      <div class="container mx-auto">
        <div class="text-center mb-16" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-neutral-900">关于轩轩</h2>
          <div class="w-20 h-1 bg-primary-800 mx-auto"></div>
        </div>

        <div class="max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">
          <p class="text-lg text-neutral-700 mb-6">
            我是轩轩，一名专注于技术分享的开发者。致力于收集、整理和分享优质的技术资源，帮助更多人提升工作效率和学习体验。
          </p>
          <p class="text-lg text-neutral-700 mb-6">
            在这里，你可以找到精心筛选的开发工具、系统优化软件、学习资料等各类实用资源，所有资源都经过我的亲自测试和验证。
          </p>
          <p class="text-lg text-neutral-700">
            如果你有好的资源推荐或者需要特定的工具，欢迎加入我们的交流群一起讨论！
          </p>
        </div>
      </div>
    </section>

    <section class="section bg-neutral-50">
      <div class="container mx-auto">
        <div class="text-center mb-16" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-4 text-neutral-900">热门资源</h2>
          <div class="w-20 h-1 bg-primary-800 mx-auto"></div>
          <p class="mt-4 text-neutral-600 max-w-2xl mx-auto">
            这里展示了最受欢迎的资源分类，更多精彩内容请访问资源库页面。
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(project, index) in featuredProjects" :key="index" class="card" data-aos="fade-up"
            :data-aos-delay="index * 100">
            <div class="p-6">
              <h3 class="text-xl font-bold mb-3 text-gray-800">{{ project.name }}</h3>
              <p class="text-gray-600 mb-4">{{ project.description }}</p>
              <a :href="project.github" target="_blank" rel="noopener noreferrer"
                class="text-primary-500 hover:text-primary-600 inline-flex items-center">
                <span>查看项目</span>
                <i class="fas fa-external-link-alt ml-2 text-sm"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="text-center mt-12" data-aos="fade-up">
          <router-link to="/projects" class="btn btn-outline">
            查看全部项目
          </router-link>
        </div>
      </div>
    </section>

    <section class="section bg-white">
      <div class="container mx-auto">
        <div class="text-center mb-16" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">最新文章</h2>
          <div class="w-20 h-1 bg-primary-500 mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div v-for="(article, index) in latestArticles" :key="index" class="card" data-aos="fade-up"
            :data-aos-delay="index * 100">
            <div class="p-6">
              <h3 class="text-xl font-bold mb-3 text-gray-800">{{ article.title }}</h3>
              <p class="text-gray-600 mb-4">{{ article.summary }}</p>
              <router-link :to="`/article/${article.id}`"
                class="text-primary-500 hover:text-primary-600 inline-flex items-center">
                <span>阅读全文</span>
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
              </router-link>
            </div>
          </div>
        </div>

        <div class="text-center mt-12" data-aos="fade-up">
          <router-link to="/articles" class="btn btn-outline">
            查看全部文章
          </router-link>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import siteConfig from '../config/site';

export default {
  name: 'Home',
  setup() {
    return {
      featuredProjects: siteConfig.featuredProjects,
      latestArticles: siteConfig.latestArticles,
      siteConfig
    };
  }
}
</script>

<style scoped>
/* 暗色模式适配 */
:deep(.dark .card) {
  @apply bg-gray-800 hover:bg-gray-750 shadow-lg shadow-gray-900/10;
}

:deep(.dark .card h3) {
  @apply text-white;
}

:deep(.dark .card p) {
  @apply text-gray-300;
}

:deep(.dark .btn-outline) {
  @apply border-primary-400 text-primary-400 hover:bg-primary-500 hover:border-primary-500;
}

/* 总之就是非常可爱按钮样式 */
.kawaii-btn {
  @apply inline-flex items-center justify-center px-6 py-3 rounded-full font-medium transition-all duration-300;
  background: linear-gradient(to right, #ffb7c5, #d8aef2);
  color: #fff;
  border: 2px solid #fff;
  box-shadow: 0 4px 10px rgba(255, 183, 197, 0.5);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

.kawaii-btn:hover {
  background: linear-gradient(to right, #ff9eb2, #c89ce0);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(255, 183, 197, 0.6);
}

.kawaii-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(255, 183, 197, 0.4);
}
</style>