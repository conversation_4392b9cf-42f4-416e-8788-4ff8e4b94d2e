<template>
  <div>
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary-50 to-neutral-50 py-16">
      <div class="container mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-neutral-900" data-aos="fade-up">
          <i class="fas fa-download mr-4 text-primary-800"></i>
          资源库
        </h1>
        <div class="w-20 h-1 bg-primary-800 mx-auto mb-6" data-aos="fade-up" data-aos-delay="100"></div>
        <p class="text-lg text-neutral-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
          精选的技术资源、开发工具、系统优化软件和学习资料，助力提升你的工作效率
        </p>
      </div>
    </section>

    <!-- Resource Categories -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12" data-aos="fade-up">
          <h2 class="text-3xl font-bold text-neutral-900 mb-4">资源分类</h2>
          <p class="text-neutral-600">选择你需要的资源类型</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(category, index) in resourceCategories" :key="category.id"
               class="resource-card p-6 cursor-pointer"
               data-aos="fade-up"
               :data-aos-delay="index * 100"
               @click="selectCategory(category.id)">
            <div class="text-center">
              <div class="w-16 h-16 mx-auto mb-4 rounded-full gradient-accent flex items-center justify-center">
                <i :class="category.icon" class="text-2xl text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-neutral-800 mb-2">{{ category.name }}</h3>
              <p class="text-neutral-600 mb-4">{{ category.description }}</p>
              <div class="text-sm text-primary-800 font-medium">
                {{ category.count }} 个资源
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Resource List -->
    <section class="py-16 bg-neutral-50">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center mb-8">
          <h2 class="text-2xl font-bold text-neutral-900">
            {{ selectedCategory ? getCategoryName(selectedCategory) : '所有资源' }}
          </h2>
          <div class="flex items-center space-x-4">
            <select v-model="selectedCategory" class="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-800 text-neutral-700">
              <option value="">所有分类</option>
              <option v-for="category in resourceCategories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="(resource, index) in filteredResources" :key="resource.id"
               class="resource-card p-6"
               data-aos="fade-up"
               :data-aos-delay="(index % 3) * 100">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h3 class="text-lg font-bold text-neutral-800 mb-2">{{ resource.name }}</h3>
                <div class="flex items-center space-x-2 mb-2">
                  <span class="tech-tag">{{ getCategoryName(resource.category) }}</span>
                  <span v-if="resource.platform" class="tech-tag">{{ resource.platform }}</span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span v-if="resource.rating" class="flex items-center text-warning-600">
                  <i class="fas fa-star mr-1"></i>
                  {{ resource.rating }}
                </span>
              </div>
            </div>

            <p class="text-neutral-600 mb-4 text-sm">{{ resource.description }}</p>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2 text-sm text-neutral-500">
                <span v-if="resource.size">{{ resource.size }}</span>
                <span v-if="resource.version">v{{ resource.version }}</span>
              </div>
              <div class="flex space-x-2">
                <a v-if="resource.downloadUrl" :href="resource.downloadUrl" target="_blank"
                   class="btn btn-primary btn-sm">
                  <i class="fas fa-download mr-1"></i>
                  下载
                </a>
                <a v-if="resource.demoUrl" :href="resource.demoUrl" target="_blank"
                   class="btn btn-outline btn-sm">
                  <i class="fas fa-eye mr-1"></i>
                  预览
                </a>
              </div>
            </div>
          </div>
        </div>

        <div v-if="filteredResources.length === 0" class="text-center py-16">
          <i class="fas fa-search text-5xl text-neutral-300 mb-4"></i>
          <p class="text-xl text-neutral-500">暂无相关资源</p>
          <p class="text-neutral-400 mt-2">请尝试选择其他分类或稍后再来查看</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import AOS from 'aos';

// 响应式数据
const selectedCategory = ref('');

// 资源分类
const resourceCategories = ref([
  {
    id: 'dev-tools',
    name: '开发工具',
    description: 'IDE、编辑器、调试工具等开发必备软件',
    icon: 'fas fa-code',
    count: 15
  },
  {
    id: 'system-tools',
    name: '系统工具',
    description: '系统优化、清理、安全防护等实用工具',
    icon: 'fas fa-cogs',
    count: 12
  },
  {
    id: 'design-tools',
    name: '设计工具',
    description: '图像处理、UI设计、原型制作等设计软件',
    icon: 'fas fa-palette',
    count: 8
  },
  {
    id: 'learning',
    name: '学习资源',
    description: '技术文档、视频教程、电子书等学习材料',
    icon: 'fas fa-book',
    count: 20
  },
  {
    id: 'productivity',
    name: '效率工具',
    description: '时间管理、笔记软件、自动化工具等',
    icon: 'fas fa-rocket',
    count: 10
  },
  {
    id: 'media-tools',
    name: '媒体工具',
    description: '音视频处理、格式转换、录屏软件等',
    icon: 'fas fa-video',
    count: 7
  }
]);

// 资源数据
const resources = ref([
  // 开发工具
  {
    id: 1,
    name: 'Visual Studio Code',
    category: 'dev-tools',
    description: '微软开发的免费代码编辑器，支持多种编程语言，拥有丰富的插件生态',
    platform: 'Windows/Mac/Linux',
    version: '1.85.0',
    size: '85MB',
    rating: 4.9,
    downloadUrl: 'https://code.visualstudio.com/download',
    demoUrl: 'https://vscode.dev'
  },
  {
    id: 2,
    name: 'JetBrains IntelliJ IDEA',
    category: 'dev-tools',
    description: '强大的Java IDE，支持多种JVM语言，智能代码补全和重构功能',
    platform: 'Windows/Mac/Linux',
    version: '2023.3',
    size: '800MB',
    rating: 4.8,
    downloadUrl: 'https://www.jetbrains.com/idea/download/',
    demoUrl: null
  },
  {
    id: 3,
    name: 'Postman',
    category: 'dev-tools',
    description: 'API开发和测试工具，支持REST、GraphQL等多种API类型',
    platform: 'Windows/Mac/Linux',
    version: '10.20.0',
    size: '150MB',
    rating: 4.7,
    downloadUrl: 'https://www.postman.com/downloads/',
    demoUrl: null
  },
  // 系统工具
  {
    id: 4,
    name: 'CCleaner',
    category: 'system-tools',
    description: '系统清理和优化工具，清理垃圾文件，保护隐私',
    platform: 'Windows/Mac',
    version: '6.18',
    size: '25MB',
    rating: 4.5,
    downloadUrl: 'https://www.ccleaner.com/ccleaner/download',
    demoUrl: null
  },
  {
    id: 5,
    name: 'Process Monitor',
    category: 'system-tools',
    description: '微软官方系统监控工具，实时监控文件系统、注册表和进程活动',
    platform: 'Windows',
    version: '3.89',
    size: '2MB',
    rating: 4.8,
    downloadUrl: 'https://docs.microsoft.com/en-us/sysinternals/downloads/procmon',
    demoUrl: null
  },
  // 设计工具
  {
    id: 6,
    name: 'Figma',
    category: 'design-tools',
    description: '基于浏览器的协作式UI设计工具，支持实时协作和原型制作',
    platform: 'Web/Windows/Mac',
    version: 'Latest',
    size: '在线工具',
    rating: 4.9,
    downloadUrl: 'https://www.figma.com/downloads/',
    demoUrl: 'https://www.figma.com'
  },
  {
    id: 7,
    name: 'GIMP',
    category: 'design-tools',
    description: '免费开源的图像编辑软件，功能强大，可替代Photoshop',
    platform: 'Windows/Mac/Linux',
    version: '2.10.34',
    size: '200MB',
    rating: 4.3,
    downloadUrl: 'https://www.gimp.org/downloads/',
    demoUrl: null
  },
  // 学习资源
  {
    id: 8,
    name: 'MDN Web Docs',
    category: 'learning',
    description: 'Mozilla维护的Web开发文档，最权威的前端技术参考资料',
    platform: 'Web',
    version: 'Latest',
    size: '在线资源',
    rating: 4.9,
    downloadUrl: null,
    demoUrl: 'https://developer.mozilla.org'
  },
  {
    id: 9,
    name: 'Vue.js 官方文档',
    category: 'learning',
    description: 'Vue.js框架的官方中文文档，从入门到进阶的完整学习资料',
    platform: 'Web',
    version: 'Vue 3',
    size: '在线资源',
    rating: 4.8,
    downloadUrl: null,
    demoUrl: 'https://cn.vuejs.org'
  },
  // 效率工具
  {
    id: 10,
    name: 'Notion',
    category: 'productivity',
    description: '全能的笔记和协作工具，支持文档、数据库、看板等多种形式',
    platform: 'Web/Windows/Mac/Mobile',
    version: 'Latest',
    size: '100MB',
    rating: 4.7,
    downloadUrl: 'https://www.notion.so/desktop',
    demoUrl: 'https://www.notion.so'
  }
]);

// 计算属性
const filteredResources = computed(() => {
  if (!selectedCategory.value) {
    return resources.value;
  }
  return resources.value.filter(resource => resource.category === selectedCategory.value);
});

// 方法
const selectCategory = (categoryId) => {
  selectedCategory.value = selectedCategory.value === categoryId ? '' : categoryId;
};

const getCategoryName = (categoryId) => {
  const category = resourceCategories.value.find(cat => cat.id === categoryId);
  return category ? category.name : '未知分类';
};

onMounted(() => {
  AOS.init({
    duration: 800,
    once: true
  });
});
</script>

<style scoped>
/* 资源卡片悬停效果 */
.resource-card {
  transition: all 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 分类选择器样式 */
select {
  transition: all 0.3s ease;
}

select:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* 按钮样式增强 */
.btn-tech {
  transition: all 0.3s ease;
}

.btn-tech:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}
</style>