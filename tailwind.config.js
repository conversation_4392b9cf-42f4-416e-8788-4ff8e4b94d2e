/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 主色系 - 深蓝色 (专业、可信赖)
        primary: {
          50: '#eff6ff',   // 最浅蓝
          100: '#dbeafe',  // 浅蓝
          200: '#bfdbfe',  //
          300: '#93c5fd',  //
          400: '#60a5fa',  //
          500: '#3b82f6',  // 标准蓝
          600: '#2563eb',  // 深蓝
          700: '#1d4ed8',  // 更深蓝
          800: '#1e40af',  // 核心主色 - 专业深蓝
          900: '#1e3a8a',  // 最深蓝
        },

        // 辅助色系 - 橙色 (活力、创新)
        secondary: {
          50: '#fff7ed',   // 最浅橙
          100: '#ffedd5',  // 浅橙
          200: '#fed7aa',  //
          300: '#fdba74',  //
          400: '#fb923c',  //
          500: '#f97316',  // 标准橙
          600: '#ea580c',  // 核心辅助色 - 活力橙
          700: '#c2410c',  // 深橙
          800: '#9a3412',  // 更深橙
          900: '#7c2d12',  // 最深橙
        },

        // 中性色系 - 灰色 (平衡、可读性)
        neutral: {
          50: '#f9fafb',   // 背景浅灰
          100: '#f3f4f6',  // 浅灰
          200: '#e5e7eb',  //
          300: '#d1d5db',  // 边框灰
          400: '#9ca3af',  //
          500: '#6b7280',  // 次要文本灰
          600: '#4b5563',  //
          700: '#374151',  //
          800: '#1f2937',  // 主要文本深灰
          900: '#111827',  // 最深灰
        },

        // 功能色系
        success: {
          50: '#ecfdf5',
          500: '#10b981',
          600: '#059669',   // 成功色
          700: '#047857',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',   // 警告色
          700: '#b45309',
        },
        error: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',   // 错误色
          700: '#b91c1c',
        },
        info: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',   // 信息色
          700: '#1d4ed8',
        },

        // 保留原有的gray配置以兼容现有代码
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          750: '#2d3748',  // 自定义中间色
          800: '#1f2937',
          900: '#111827',
        }
      },
      fontFamily: {
        sans: ['Noto Sans SC', 'sans-serif'],
        serif: ['Noto Serif SC', 'serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
} 